package sirobilt.meghasanjivini.appointment

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.ws.rs.NotFoundException
import jakarta.ws.rs.WebApplicationException
import jakarta.ws.rs.core.Response
import org.acme.appointment.dto.RescheduleAppointmentDTO
import org.acme.appointmentconfig.repositories.AppointmentSlotRepo


import sirobilt.meghasanjivini.appointment.dto.*
import sirobilt.meghasanjivini.appointmentconfig.AppointmentSlot


import sirobilt.meghasanjivini.masterdata.service.FacilityService
import sirobilt.meghasanjivini.patientregistration.service.PatientService
import sirobilt.meghasanjivini.common.enums.*
import sirobilt.meghasanjivini.masterdata.repository.FacilityStaffRepository
import java.time.*
import java.util.UUID


class BadRequestException(
    val errorCode: String,
    override val message: String
) : RuntimeException(message)



@ApplicationScoped
class AppointmentService @Inject constructor(
    private val repository: AppointmentRepository,
    private val patientService: PatientService,
    private val facilityService: FacilityService,
    private val slotRepo: AppointmentSlotRepo,
    private val doctorRepo: DoctorRepository


) {



    fun findAll(): List<AppointmentDTO> =
        repository.listAll().map { toDTO(it) }

    fun findById(id: String): AppointmentDTO {
        val e = repository.findById(id)
            ?: throw NotFoundException("Appointment with id $id not found")
        return toDTO(e)
    }

    @Transactional
    fun create(dto: AppointmentDTO, changedBy: String): AppointmentResponseDTO {
        println("Entered into the create function")
        val now = LocalDateTime.now()
        val today = LocalDate.now()
        val appointmentDate = dto.appointmentDate.toLocalDate()

        if (appointmentDate.isBefore(today)) {
            throw BadRequestException(
                errorCode = "PAST_DATE_NOT_ALLOWED",
                message = "Cannot create an appointment in the past"
            )
        }

        val patientId = dto.patientId
        val providerId = dto.providerId
        val date = dto.appointmentDate.toLocalDate()

        if (repository.existsAppointmentForPatientProviderOnDate(patientId, providerId, date)) {
            throw BadRequestException(
                errorCode = "APPOINTMENT_ALREADY_EXISTS",
                message = "Patient already has an appointment with this provider on $date"
            )
        }

        val entity = toEntity(dto).apply {
            id = dto.id ?: UUID.randomUUID().toString()
            createdAt = now
            updatedAt = createdAt
            slotId = dto.slotNumber
        }

        val slot = slotRepo.findSlotByConsultantDateAndNumber(
            UUID.fromString(providerId),
            date,
            dto.slotNumber
        )

        if (slot?.availability != SlotAvailability.OPEN) {
            throw BadRequestException(
                errorCode = "SLOT_UNAVAILABLE",
                message = "Slot ${dto.slotNumber} is not available"
            )
        }

        slot.availability = SlotAvailability.BLOCKED
        slotRepo.persist(slot)

        repository.persist(entity)

        val saved = repository.findById(entity.id)
            ?: throw NotFoundException("Appointment with id ${entity.id} not found after persist")

        return toResponseDTO(saved)
    }



    fun getServiceQueue(
        serviceType: String,
        facilityId: String,
        date: LocalDate = LocalDate.now()
    ): QueueStatusDto {
        val appointments = repository.getQueueAppointments(serviceType, facilityId, date)
        if (appointments.isEmpty()) {
            return QueueStatusDto(
                serviceType, facilityId, date.toString(),
                0, null, emptyList(), 0, 0
            )
        }

        // Calculate average service time (duration in minutes)
        val avgServiceTime = if (appointments.isNotEmpty())
            appointments.map { it.duration }.average().toInt() else 15

        // Find the currently serving: First with status "Confirmed", else first "Waiting"
        val currentlyServingIdx = appointments.indexOfFirst { it.status == "Confirmed" }
            .takeIf { it >= 0 } ?: 0

        val currentlyServingEntity = appointments.getOrNull(currentlyServingIdx)
        val currentServingPatient = currentlyServingEntity?.let {
            CurrentServingDto(
                appointmentId = it.id,
                patientName = listOfNotNull(it.patient.firstName, it.patient.lastName).joinToString(" "),
                queueNumber = currentlyServingIdx + 1,
                estimatedServiceTime = it.startTime.toLocalTime().toString()
            )
        }

        // Build the rest of the queue
        val queueList = appointments.drop(currentlyServingIdx + 1).mapIndexed { idx, a ->
            val queueNumber = currentlyServingIdx + idx + 2
            val estServiceTime = a.startTime.toLocalTime().plusMinutes((idx * avgServiceTime).toLong())
            QueuePatientDto(
                appointmentId = a.id,
                patientId = a.patient.upId,
                patientName = listOfNotNull(a.patient.firstName, a.patient.lastName).joinToString(" "),
                queueNumber = queueNumber,
                estimatedWaitTime = (idx + 1) * avgServiceTime,
                estimatedServiceTime = estServiceTime.toString(),
                priority = a.priority,
                status = a.status
            )
        }

        return QueueStatusDto(
            serviceType = serviceType,
            facilityId = facilityId,
            date = date.toString(),
            totalInQueue = appointments.size,
            currentlyServing = currentServingPatient,
            queue = queueList,
            averageServiceTime = avgServiceTime,
            estimatedWaitTime = queueList.sumOf { it.estimatedWaitTime }
        )
    }


    @Transactional
    fun addToQueue(serviceType: String, dto: AddToQueueRequestDto): AppointmentResponseDTO {
        val appointmentTypeEnum = AppointmentType.fromValue(serviceType)
            ?: throw WebApplicationException("Unknown serviceType '$serviceType'. Valid types: ${AppointmentType.values().map { it.value }}", 400)

        val entity = repository.findById(dto.appointmentId)
            ?: throw NotFoundException("Appointment with id ${dto.appointmentId} not found")

        // Validate facility and appointment type
        if (entity.type != appointmentTypeEnum)
            throw WebApplicationException("Appointment is not of type $serviceType", 400)
        if (entity.facilityId.hospitalId != dto.facilityId)
            throw WebApplicationException("Appointment does not belong to facility ${dto.facilityId}", 400)

        // Update priority, set status to "Waiting" (added to queue)
        entity.priority = dto.priority
        entity.status = "Waiting"
        entity.updatedAt = java.time.LocalDateTime.now()
        repository.persist(entity)

        return toResponseDTO(entity)
    }

    @Transactional
    fun updateQueueStatus(
        serviceType: String,
        appointmentId: String,
        dto: UpdateQueueStatusDto
    ): AppointmentResponseDTO {
        val appointmentTypeEnum = AppointmentType.fromValue(serviceType)
            ?: throw WebApplicationException("Unknown serviceType '$serviceType'. Valid types: ${AppointmentType.values().map { it.value }}", 400)
        val entity = repository.findById(appointmentId)
            ?: throw NotFoundException("Appointment with id $appointmentId not found")

        // Type check (serviceType must match)
        if (entity.type != appointmentTypeEnum)
            throw WebApplicationException("Appointment is not of type $serviceType", 400)

        // Validate status
        val allowedStatuses = setOf("Waiting", "Called", "InService", "Completed", "NoShow")
        if (dto.status !in allowedStatuses)
            throw WebApplicationException("Invalid status '${dto.status}'. Allowed: $allowedStatuses", 400)

        // DB update via repository
        repository.updateQueueStatusAndNotes(
            appointmentId = appointmentId,
            newStatus = dto.status,
            notes = dto.notes
        ) ?: throw NotFoundException("Appointment with id $appointmentId not found after update")

        return toResponseDTO(entity)
    }

    fun getWaitTimeEstimation(
        serviceType: String,
        facilityId: String,
        priority: String? = null
    ): WaitTimeEstimationDto {
        val queue = repository.getAppointmentsInQueue(serviceType, facilityId)
        val priorities = listOf("Emergency", "Urgent", "High", "Normal", "Low")

        // Calculate average service time
        val averageServiceTime = if (queue.isNotEmpty()) queue.map { it.duration }.average().toInt() else 15

        // Calculate estimated wait time and position for this priority
        val filteredQueue = if (priority != null) queue.filter { it.priority == priority } else queue

        val queuePosition = if (filteredQueue.isNotEmpty()) filteredQueue.size else 0
        val patientsAhead = if (queuePosition > 0) queuePosition - 1 else 0
        val estimatedWaitTime = patientsAhead * averageServiceTime

        // For each priority, compute wait time
        val currentWaitTime: MutableMap<String, Int> = mutableMapOf()
        for (p in priorities) {
            val count = queue.count { it.priority == p }
            currentWaitTime[p] = if (count > 0) (count - 1) * averageServiceTime else 0
        }

        return WaitTimeEstimationDto(
            serviceType = serviceType,
            facilityId = facilityId,
            estimatedWaitTime = estimatedWaitTime,
            queuePosition = queuePosition,
            patientsAhead = patientsAhead,
            averageServiceTime = averageServiceTime,
            currentWaitTime = currentWaitTime,
            lastUpdated = OffsetDateTime.now(ZoneOffset.UTC).toString()
        )
    }


    @Transactional
    fun update(id: String, dto: AppointmentDTO,  changedBy: String ): AppointmentDTO {
        val e = repository.findById(id)
            ?: throw NotFoundException("Appointment with id $id not found")
        val oldDTO = toDTO(e)

        if (e.appointmentDate.isBefore(LocalDateTime.now())) {
            throw WebApplicationException(
                Response.status(Response.Status.BAD_REQUEST)
                    .entity(ApiResponse(false, "Past appointments cannot be updated"))
                    .build()
            )
        }


        e.slotId.let { oldSlotId ->
            val oldSlot = slotRepo.findById(oldSlotId)
                ?: throw NotFoundException("Old slot $oldSlotId not found")
            oldSlot.availability = SlotAvailability.OPEN
            slotRepo.persist(oldSlot)
        }
        // associations
        e.patient    = patientService.findEntityById(dto.patientId)
        e.facilityId = facilityService.findEntityById(dto.facilityId)
        e.providerId = dto.providerId

        // simple scalar fields
        with(e) {
            appointmentDate   = dto.appointmentDate
            startTime         = dto.startTime
            endTime           = dto.endTime
            duration          = dto.duration
            type              = AppointmentType.valueOf(dto.type)
            status            = dto.status
            priority          = dto.priority
            title             = dto.title
            description       = dto.description
            notes             = dto.notes
            reason            = dto.reason
            isRecurring       = dto.isRecurring
            recurringPattern    = dto.recurringPattern?.let { pattern ->
                if (pattern.isBlank()) null else RecurringPattern.fromValue(pattern)
            }

            recurringEndDate  = dto.recurringEndDate
            parentAppointmentId = dto.parentAppointmentId
            externalSystemId    = dto.externalSystemId
            externalSystemName  = dto.externalSystemName
            updatedAt           = LocalDateTime.now()
        }

        repository.persist(e)
        val newDTO = toDTO(e)
        val changedFields = diffAppointmentDTO(oldDTO, newDTO)


        val changeSummary = if (changedFields.isEmpty()) {
            "No fields changed."
        } else {
            changedFields.entries.joinToString("; ") { (field, values) ->
                "$field: '${values.first}' → '${values.second}'"
            }
        }



        return toDTO(e)
    }

    @Transactional
    fun delete(id: String) {
        if (!repository.deleteById(id)) {
            throw NotFoundException("Appointment with id $id not found")
        }
    }

    private fun toDTO(e: AppointmentEntity): AppointmentDTO =
        AppointmentDTO(
            id                  = e.id,
            patientId           = e.patient.upId,
            providerId          = e.providerId,
            facilityId          = e.facilityId.hospitalId,
            appointmentDate     = e.appointmentDate,
            startTime           = e.startTime,
            endTime             = e.endTime,
            duration            = e.duration,
            type                = e.type.name,
            status              = e.status,
            priority            = e.priority,
            title               = e.title,
            description         = e.description,
            notes               = e.notes,
            reason              = e.reason,
            isRecurring         = e.isRecurring,
            recurringPattern    = e.recurringPattern.toString(),
            recurringEndDate    = e.recurringEndDate,
            parentAppointmentId = e.parentAppointmentId,
            externalSystemId    = e.externalSystemId,
            externalSystemName  = e.externalSystemName,
            createdBy           = e.createdBy,
            updatedBy           = e.updatedBy,
            slotNumber = e.slotId
        )

    private fun toEntity(dto: AppointmentDTO): AppointmentEntity {
        println("Mapping AppointmentDTO to AppointmentEntity: $dto")
        val entity = AppointmentEntity()

        try {
            dto.id?.let { entity.id = it }

            entity.patient = patientService.findEntityById(dto.patientId)
            entity.providerId = dto.providerId
            entity.facilityId = facilityService.findEntityById(dto.facilityId)
            entity.appointmentDate = dto.appointmentDate
            entity.startTime = dto.startTime
            entity.endTime = dto.endTime
            entity.duration = dto.duration
            entity.type = AppointmentType.valueOf(dto.type)
            entity.status = dto.status
            entity.priority = dto.priority
            entity.title = dto.title
            entity.description = dto.description
            entity.notes = dto.notes
            entity.reason = dto.reason
            entity.isRecurring = dto.isRecurring

            // ✅ Updated recurringPattern assignment
            entity.recurringPattern = dto.recurringPattern
                ?.takeIf { it.isNotBlank() }
                ?.let { RecurringPattern.fromValue(it) }

            entity.recurringEndDate = dto.recurringEndDate
            entity.parentAppointmentId = dto.parentAppointmentId
            entity.externalSystemId = dto.externalSystemId
            entity.externalSystemName = dto.externalSystemName
            entity.createdBy = dto.createdBy ?: "system"
            entity.updatedBy = dto.updatedBy ?: "system"

        } catch (ex: Exception) {
            ex.printStackTrace()
            throw ex
        }

        return entity
    }

    fun getAppointmentsWithFilters(
        patientId: String?,
        providerId: String?,
        facilityId: String?,
        status: List<String>?,
        type: List<String>?,
        priority: List<String>?,
        dateFrom: String?,
        dateTo: String?,
        searchTerm: String?,
        page: Int,
        size: Int
    ): PaginatedResponse<Map<String, List<AppointmentResponseDTO>>> {
        val (appointments, total) = repository.searchAppointments(
            patientId, providerId, facilityId, status, type, priority, dateFrom, dateTo, searchTerm, page, size
        )
        val responseList = appointments.map { toResponseDTO(it) }
        val totalPages = if (size == 0) 0 else ((total + size - 1) / size).toInt()
        return PaginatedResponse(
            success = true,
            data = mapOf("appointments" to responseList),
            pagination = Pagination(
                page = page,
                size = size,
                totalElements = total,
                totalPages = totalPages
            )
        )
    }

    @Transactional
    fun cancelAppointment(id: String, dto: CancelAppointmentDto): AppointmentResponseDTO {
        val appointment = repository.findById(id)
            ?: throw NotFoundException("Appointment with id $id not found")

        if (appointment.status == "Cancelled") {
            throw WebApplicationException(
                Response.status(Response.Status.CONFLICT)
                    .entity(ApiResponse(false, "Appointment is already cancelled"))
                    .build()
            )
        }

        appointment.slotId.let { slotId ->
            val slot = slotRepo.findSlotByConsultantDateAndNumber(UUID.fromString(appointment.providerId),appointment.appointmentDate.toLocalDate(),slotId)
                ?: throw NotFoundException("Slot with id $slotId not found")
            slot.availability = SlotAvailability.OPEN
            slotRepo.persist(slot)
        }

        if (appointment.status == "Cancelled") {
            throw WebApplicationException("Appointment is already cancelled", Response.Status.CONFLICT)
        }

        appointment.status = "Cancelled"
        appointment.reason = dto.reason
        appointment.notes = dto.notes
        appointment.updatedAt = LocalDateTime.now()
        // appointment.updatedBy = ... // fill from auth/user context if available

        repository.persist(appointment)



        return toResponseDTO(appointment)
    }

    @Transactional
    fun confirmAppointment(id: String, dto: ConfirmAppointmentDto): AppointmentResponseDTO {
        val appointment = repository.findById(id)
            ?: throw NotFoundException("Appointment with id $id not found")

        val today = LocalDate.now()
        if (appointment.appointmentDate.toLocalDate().isBefore(today)) {
            throw WebApplicationException(
                Response.status(Response.Status.BAD_REQUEST)
                    .entity(ApiResponse(false, "Past appointments cannot be confirmed"))
                    .build()
            )
        }

        if (appointment.status == "Confirmed") {
            throw WebApplicationException(
                Response.status(Response.Status.CONFLICT)
                    .entity(ApiResponse(false, "Appointment is already confirmed"))
                    .build()
            )
        }
        if (appointment.status == "Cancelled") {
            throw WebApplicationException(
                Response.status(Response.Status.CONFLICT)
                    .entity(ApiResponse(false, "Cancelled appointment cannot be confirmed"))
                    .build()
            )
        }

        appointment.status = "Confirmed"
        if (dto.notes != null) {
            appointment.notes = dto.notes
        }
        appointment.updatedAt = LocalDateTime.now()


        repository.persist(appointment)



        return toResponseDTO(appointment)
    }

    @Transactional
    fun rescheduleAppointment(id: String, dto: RescheduleAppointmentDTO): AppointmentResponseDTO {
        val appointment = repository.findById(id)
            ?: throw NotFoundException("Appointment with id $id not found")

        if (appointment.status == "Cancelled") {
            throw WebApplicationException(
                Response.status(Response.Status.CONFLICT)
                    .entity(ApiResponse(false, "Cancelled appointment cannot be rescheduled"))
                    .build()
            )
        }

        // Mark old slot as OPEN
        appointment.slotId.let { oldSlotId ->
            val oldSlot = slotRepo.findById(oldSlotId)
                ?: throw NotFoundException("Old slot $oldSlotId not found")
            oldSlot.availability = SlotAvailability.OPEN
            slotRepo.persist(oldSlot)
        }

        // Validate new slot
        val newSlot = slotRepo.findById(dto.newSlotId)
            ?: throw NotFoundException("New slot ${dto.newSlotId} not found")

        if (newSlot.availability != SlotAvailability.OPEN) {
            throw WebApplicationException(
                Response.status(Response.Status.CONFLICT)
                    .entity(ApiResponse(false, "New slot is not available"))
                    .build()
            )
        }

        // Mark new slot as BOOKED
        newSlot.availability = SlotAvailability.BOOKED
        slotRepo.persist(newSlot)

        // Update appointment details
        appointment.apply {
            slotId = dto.newSlotId
            appointmentDate = newSlot.slotDate.atStartOfDay()
            startTime = LocalDateTime.of(newSlot.slotDate, newSlot.startTime)
            endTime = LocalDateTime.of(newSlot.slotDate, newSlot.endTime)
            duration = (newSlot.endTime.toSecondOfDay() - newSlot.startTime.toSecondOfDay()) / 60
            providerId = dto.newPractitionerId?.toString() ?: providerId
            notes = dto.rescheduleReason
            updatedAt = LocalDateTime.now()

            repository.persist(appointment)

            // TODO: Add notification logic if dto.notifyPatient is true

            return toResponseDTO(appointment)
        }
    }


    @Transactional
    fun deleteAppointment(id: String, reason: String) {
        val appointment = repository.findById(id)
            ?: throw NotFoundException("Appointment with id $id not found")

        if (appointment.status == "Deleted") {
            throw WebApplicationException(
                Response.status(Response.Status.CONFLICT)
                    .entity(ApiResponse(false, "Appointment is already deleted"))
                    .build()
            )
        }

        appointment.status = "Deleted"
        appointment.reason = reason
        appointment.updatedAt = LocalDateTime.now()
        // appointment.updatedBy = ... // set if you have user context

        repository.persist(appointment)
    }

    fun getAppointmentStatistics(
        facilityId: String?,
        dateFrom: String?,
        dateTo: String?
    ): AppointmentStatisticsDto {
        val counts = repository.getAppointmentCounts(facilityId, dateFrom, dateTo)
        val byType = repository.getAppointmentsByType(facilityId, dateFrom, dateTo)
        val byPriority = repository.getAppointmentsByPriority(facilityId, dateFrom, dateTo)
        val (today, upcoming) = repository.getTodayAndUpcomingCounts(facilityId, dateFrom, dateTo)

        return AppointmentStatisticsDto(
            totalAppointments = counts.values.sum(),
            scheduledAppointments = counts["Scheduled"] ?: 0,
            confirmedAppointments = counts["Confirmed"] ?: 0,
            completedAppointments = counts["Completed"] ?: 0,
            cancelledAppointments = counts["Cancelled"] ?: 0,
            noShowAppointments = counts["NoShow"] ?: 0,
            todayAppointments = today,
            upcomingAppointments = upcoming,
            appointmentsByType = byType,
            appointmentsByPriority = byPriority,
            averageWaitTime = 25.5,            // Fetch from analytics or calculation if available
            patientSatisfactionScore = 4.2     // Fetch from feedback if available
        )
    }

    fun getTodaysAppointmentsBrief(
        facilityId: String?,
        providerId: String?
    ): List<AppointmentBriefDTO> {
        val today = LocalDate.now()
        val entities = repository.findBriefAppointments(
            facilityId = facilityId,
            providerId = providerId,
            dateFrom = today,
            dateTo = today
        )
        return entities.map { toBriefDTO(it) }
    }

    fun getUpcomingAppointmentsBrief(
        facilityId: String?,
        providerId: String?,
        days: Int = 7
    ): List<AppointmentBriefDTO> {
        val startDate = LocalDate.now().plusDays(1)
        val endDate = startDate.plusDays(days.toLong() - 1)
        val entities = repository.findBriefAppointments(
            facilityId = facilityId,
            providerId = providerId,
            dateFrom = startDate,
            dateTo = endDate
        )
        return entities.map { toBriefDTO(it) }
    }

    // Get Queue Statistics
    fun getQueueStatistics(
        facilityId: String,
        serviceType: String?,
        date: LocalDate
    ): QueueStatisticsDto {
        // Fetch data
        val completed = repository.findCompleted(facilityId, date, serviceType)
        val currentInQueue = repository.countInQueue(facilityId, date, serviceType).toInt()
        val totalProcessed = completed.size
        val waitTimes = completed.map {
            Duration.between(it.appointmentDate, it.startTime).toMinutes().toInt()
        }
        val avgWait = if (waitTimes.isNotEmpty()) waitTimes.average().toInt() else 0
        val serviceTimes = completed.map { it.duration }
        val avgService = if (serviceTimes.isNotEmpty()) serviceTimes.average().toInt() else 0

        // Peak hours: top 2 busiest by completed count
        val peak = completed.groupBy { it.startTime.hour }
            .mapValues { (_, list) -> list.size }
            .entries.sortedByDescending { it.value }
            .take(2)
            .map { (hour, _) -> "%02d:00-%02d:00".format(hour, hour + 1) }

        // Service stats per type
        val types = if (serviceType != null) listOf(serviceType) else AppointmentType.values().map { it.value }
        val svcStats = types.associateWith { type ->
            val list = if (serviceType != null) completed else completed.filter { it.type.value == type }
            val wt = list.map {
                Duration.between(it.appointmentDate, it.startTime).toMinutes().toInt()
            }
            val st = list.map { it.duration }
            ServiceStatDto(
                totalProcessed = list.size,
                averageWaitTime = if (wt.isNotEmpty()) wt.average().toInt() else 0,
                averageServiceTime = if (st.isNotEmpty()) st.average().toInt() else 0
            )
        }

        // Hourly stats
        val hourly = completed.groupBy { it.startTime.hour }
            .entries.sortedBy { it.key }
            .map { (hour, list) ->
                val wt = list.map {
                    Duration.between(it.appointmentDate, it.startTime).toMinutes().toInt()
                }
                HourlyStatDto(
                    hour = "%02d:00".format(hour),
                    patientsServed = list.size,
                    averageWaitTime = if (wt.isNotEmpty()) wt.average().toInt() else 0
                )
            }

        return QueueStatisticsDto(
            facilityId = facilityId,
            date = date.toString(),
            totalProcessed = totalProcessed,
            currentInQueue = currentInQueue,
            averageWaitTime = avgWait,
            averageServiceTime = avgService,
            peakHours = peak,
            serviceStats = svcStats,
            hourlyStats = hourly
        )
    }

    // Remove from queue
    @Transactional
    fun removeFromQueue(
        serviceType: String,
        appointmentId: String,
        deleteReq: DeleteAppointmentDto
    ): AppointmentResponseDTO {
        // (Optional) verify the appointment’s type matches the path‐param:
        val entity = repository.findById(appointmentId)
            ?: throw NotFoundException("Appointment $appointmentId not found")
        if (entity.type.value.lowercase() != serviceType.lowercase()) {
            println("entered into if condition")
            throw WebApplicationException(
                "Appointment $appointmentId is not of serviceType $serviceType", 400
            )
        }

        // perform the soft‐delete
        val updated = repository.deleteQueueAppointment(
            appointmentId,
            deleteReq.reason,
            deleteReq.notes
        )

        return toResponseDTO(updated)
    }


    private fun toResponseDTO(entity: AppointmentEntity): AppointmentResponseDTO {


        val patient  = entity.patient
        val facility = entity.facilityId

        val doctor = doctorRepo.findById(UUID.fromString(entity.providerId))

        val providerFirstName = doctor?.firstName ?: ""
        val providerMiddleName = doctor?.middleName
        val providerLastName = doctor?.lastName ?: ""

        val providerFullName = listOfNotNull(providerFirstName, providerMiddleName, providerLastName)
            .joinToString(" ")

        return AppointmentResponseDTO(
            appointmentId = entity.id,
            patientId = patient.upId,
            providerId = entity.providerId,
            facilityId = facility.hospitalId,
            appointmentDate = entity.appointmentDate.toLocalDate(),
            startTime = entity.startTime.toLocalTime(),
            endTime = entity.endTime.toLocalTime(),
            duration = entity.duration,
            type = entity.type.name,
            status = entity.status,
            priority = entity.priority,
            title = entity.title,
            description = entity.description,
            notes = entity.notes,
            reason = entity.reason,
            isRecurring = entity.isRecurring,
            recurringPattern = entity.recurringPattern?.name,
            recurringEndDate = entity.recurringEndDate,
            parentAppointmentId = entity.parentAppointmentId,
            createdAt = entity.createdAt.atOffset(ZoneOffset.UTC),
            updatedAt = entity.updatedAt.atOffset(ZoneOffset.UTC),
            createdBy = entity.createdBy,
            updatedBy = entity.updatedBy,
            patient = PatientInfoDTO(
                firstName = patient.firstName,
                lastName = patient.lastName,
                fullName = patient.firstName + " " + patient.lastName

                ),
            provider = ProviderInfoDTO(
                firstName = providerFirstName,
                lastName = providerLastName,
                fullName = providerFullName
            ),
            facility = FacilityInfoDTO(
                facilityName = facility.facilityName,
                address = facility.location
            )
        )
    }

    fun toBriefDTO(entity: AppointmentEntity): AppointmentBriefDTO {
        val patient = entity.patient
        val patientName = listOfNotNull(patient.firstName, patient.lastName).joinToString(" ")
        return AppointmentBriefDTO(
            appointmentId = entity.id,
            appointmentDate = entity.appointmentDate.toLocalDate(),
            startTime = entity.startTime.toLocalTime(),
            endTime = entity.endTime.toLocalTime(),
            patientName = patientName,
            providerId = entity.providerId,
            status = entity.status,
            type = entity.type.name,
            facilityId = entity.facilityId.hospitalId
        )
    }

    fun diffAppointmentDTO(old: AppointmentDTO, new: AppointmentDTO): Map<String, Pair<Any?, Any?>> {
        val changedFields = mutableMapOf<String, Pair<Any?, Any?>>()

        // Compare each field (add more fields as needed)
        if (old.patientId != new.patientId) changedFields["patientId"] = old.patientId to new.patientId
        if (old.providerId != new.providerId) changedFields["providerId"] = old.providerId to new.providerId
        if (old.facilityId != new.facilityId) changedFields["facilityId"] = old.facilityId to new.facilityId
        if (old.appointmentDate != new.appointmentDate) changedFields["appointmentDate"] = old.appointmentDate to new.appointmentDate
        if (old.startTime != new.startTime) changedFields["startTime"] = old.startTime to new.startTime
        if (old.endTime != new.endTime) changedFields["endTime"] = old.endTime to new.endTime
        if (old.duration != new.duration) changedFields["duration"] = old.duration to new.duration
        if (old.type != new.type) changedFields["type"] = old.type to new.type
        if (old.status != new.status) changedFields["status"] = old.status to new.status
        if (old.priority != new.priority) changedFields["priority"] = old.priority to new.priority
        if (old.title != new.title) changedFields["title"] = old.title to new.title
        if (old.description != new.description) changedFields["description"] = old.description to new.description
        if (old.notes != new.notes) changedFields["notes"] = old.notes to new.notes
        if (old.reason != new.reason) changedFields["reason"] = old.reason to new.reason
        if (old.isRecurring != new.isRecurring) changedFields["isRecurring"] = old.isRecurring to new.isRecurring
        if (old.recurringPattern != new.recurringPattern) changedFields["recurringPattern"] = old.recurringPattern to new.recurringPattern
        if (old.recurringEndDate != new.recurringEndDate) changedFields["recurringEndDate"] = old.recurringEndDate to new.recurringEndDate
        if (old.parentAppointmentId != new.parentAppointmentId) changedFields["parentAppointmentId"] = old.parentAppointmentId to new.parentAppointmentId
        if (old.externalSystemId != new.externalSystemId) changedFields["externalSystemId"] = old.externalSystemId to new.externalSystemId
        if (old.externalSystemName != new.externalSystemName) changedFields["externalSystemName"] = old.externalSystemName to new.externalSystemName
        if (old.createdBy != new.createdBy) changedFields["createdBy"] = old.createdBy to new.createdBy
        if (old.updatedBy != new.updatedBy) changedFields["updatedBy"] = old.updatedBy to new.updatedBy

        return changedFields
    }



}

