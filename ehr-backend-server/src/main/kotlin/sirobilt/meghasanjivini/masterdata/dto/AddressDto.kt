package sirobilt.meghasanjivini.masterdata.dto

import sirobilt.meghasanjivini.common.model.Address

data class AddressDto(
    val addressLine1: String?,
    val addressLine2: String?,
    val city: String?,
    val state: String?,
    val postalCode: String?,
    val country: String?
) {
    companion object {
        fun fromEmbeddable(address: Address): AddressDto {
            return AddressDto(
                addressLine1 = address.addressLine1,
                addressLine2 = address.addressLine2,
                city = address.city,
                state = address.state,
                postalCode = address.postalCode,
                country = address.country
            )
        }
    }
}
