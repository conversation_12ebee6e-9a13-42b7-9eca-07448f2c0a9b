package sirobilt.meghasanjivini.masterdata.dto

import sirobilt.meghasanjivini.masterdata.model.FacilityStaff
import java.time.LocalDate
import java.time.LocalDateTime

data class StaffDto(
    val firstName: String,
    val lastName: String,
    val middleName: String? = null,
    val specializations: List<String>,
    val roleType: String,
    val qualification: String?,
    val gender: String,
    val dateOfBirth: LocalDate,
    val age: Int,
    val mobileNumber: String?,
    val email: String?,
    val registrationNumber: String?,
    val registrationState: String?,
    val yearsOfExperience: Int?,
    val telemedicineReady: Boolean?,
    val languagesSpoken: List<String>,
    val isActive: Boolean,
    val address: AddressDto,
    val facilityId: String,
    val createdAt: LocalDateTime? = null,
    val updatedAt: LocalDateTime? = null
) {
    companion object {
        fun fromEntity(staff: FacilityStaff): StaffDto {
            return StaffDto(
                firstName = staff.firstName,
                lastName = staff.lastName,
                middleName = staff.middleName,
                specializations = staff.specializations,
                roleType = staff.roleType,
                qualification = staff.qualification,
                gender = staff.gender,
                dateOfBirth = staff.dateOfBirth,
                age = staff.age,
                mobileNumber = staff.mobileNumber,
                email = staff.email,
                registrationNumber = staff.registrationNumber,
                registrationState = staff.registrationState,
                yearsOfExperience = staff.yearsOfExperience,
                telemedicineReady = staff.telemedicineReady,
                languagesSpoken = staff.languagesSpoken,
                isActive = staff.isActive,
                address = AddressDto.fromEmbeddable(staff.address),
                facilityId = staff.facilityId,
                createdAt = staff.createdAt,
                updatedAt = staff.updatedAt
            )
        }
    }
}
