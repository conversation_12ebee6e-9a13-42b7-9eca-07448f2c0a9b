package sirobilt.meghasanjivini.masterdata.repository

import io.quarkus.hibernate.orm.panache.kotlin.PanacheRepository
import jakarta.enterprise.context.ApplicationScoped
import sirobilt.meghasanjivini.common.enums.SlotAvailability
import sirobilt.meghasanjivini.masterdata.model.FacilityStaff
import java.util.*
import java.time.LocalDate

@ApplicationScoped
class FacilityStaffRepository : PanacheRepository<FacilityStaff> {

    fun findById(id: UUID): FacilityStaff? =
        find("staffId", id).firstResult()

    fun findAllActive(): List<FacilityStaff> =
        list("isActive", true)

    fun listBySpecialization(specializationId: String): List<FacilityStaff> =
        list(":specializationId = any(specializations)", mapOf("specializationId" to specializationId))

    fun deleteById(id: UUID): Boolean =
        delete("staffId = ?1", id) > 0

    fun findAvailableDoctorsForDate(date: LocalDate): List<FacilityStaff> {
        val dayOfWeek = date.dayOfWeek.name

        return find(
            """
            FROM FacilityStaff fs
            WHERE fs.isActive = true
              AND fs.roleType = 'DOCTOR'
              AND fs.staffId IN (
                  SELECT c.consultantId
                  FROM ConsultantSlotConfig c
                  JOIN c.daysOfWeek dow
                  WHERE dow = :dayOfWeek
                    AND (c.effectiveTo IS NULL OR c.effectiveTo >= :date)
                    AND c.effectiveFrom <= :date
                    AND c.consultantId IN (
                        SELECT s.consultantId
                        FROM AppointmentSlot s
                        WHERE s.availability = :availability
                          AND s.slotDate = :date
                          AND s.dayOfWeek = :dayOfWeek
                    )
              )
            """,
            mapOf(
                "dayOfWeek" to dayOfWeek,
                "availability" to SlotAvailability.OPEN,
                "date" to date
            )
        ).list()
    }

    fun findByRoleType(roleType: String): List<FacilityStaff> =
        list("roleType = ?1 AND isActive = true", roleType)

    fun findDoctors(): List<FacilityStaff> =
        findByRoleType("DOCTOR")

    fun findByFacilityId(facilityId: String): List<FacilityStaff> =
        list("facilityId = ?1 AND isActive = true", facilityId)
}
