package sirobilt.meghasanjivini.masterdata.controller

import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.ws.rs.*
import jakarta.ws.rs.core.*
import sirobilt.meghasanjivini.department.model.Department
import sirobilt.meghasanjivini.department.repository.DepartmentRepository
import sirobilt.meghasanjivini.masterdata.dto.StaffDto
import sirobilt.meghasanjivini.masterdata.dto.ListStaffDto
import sirobilt.meghasanjivini.masterdata.model.FacilityStaff
import sirobilt.meghasanjivini.masterdata.repository.FacilityStaffRepository
import sirobilt.meghasanjivini.common.model.Address
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

@Path("/staff")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
class FacilityStaffResource {

    @Inject
    lateinit var staffRepo: FacilityStaffRepository

    @Inject
    lateinit var departmentRepo: DepartmentRepository

    @GET
    fun listAll(): Response {
        val list = staffRepo.listAll().map { ListStaffDto.fromEntity(it) }
        return Response.ok(list).build()
    }

    @GET
    @Path("active")
    fun listActive(): Response {
        val active = staffRepo.findAllActive().map { StaffDto.fromEntity(it) }
        return Response.ok(active).build()
    }

    @GET
    @Path("{id}")
    fun getById(@PathParam("id") id: UUID): Response {
        val staff = staffRepo.findById(id)
            ?: return Response.status(Response.Status.NOT_FOUND).build()
        return Response.ok(StaffDto.fromEntity(staff)).build()
    }

    @GET
    @Path("specialization/{specId}")
    fun listBySpecialization(@PathParam("specId") specId: String): Response {
        val spec: Department = departmentRepo.findById(specId)
            ?: return Response.status(Response.Status.NOT_FOUND).entity("Specialization not found").build()

        val list = staffRepo.listBySpecialization(specId).map { StaffDto.fromEntity(it) }
        return Response.ok(list).build()
    }

    @GET
    @Path("available-doctors")
    fun getDoctorsAvailableOnDate(@QueryParam("date") date: LocalDate): List<ListStaffDto> {
        val doctors = staffRepo.findAvailableDoctorsForDate(date)
        return doctors.map { ListStaffDto.fromEntity(it) }
    }

    @POST
    @Transactional
    fun create(dto: StaffDto, @Context uriInfo: UriInfo): Response {
        val staff = FacilityStaff().apply {
            staffId = UUID.randomUUID()
            firstName = dto.firstName
            lastName = dto.lastName
            middleName = dto.middleName
            specializations = dto.specializations
            primarySpecialization = dto.primarySpecialization
            roleType = dto.roleType
            qualification = dto.qualification
            gender = dto.gender
            dateOfBirth = dto.dateOfBirth
            age = dto.age
            mobileNumber = dto.mobileNumber
            email = dto.email
            registrationNumber = dto.registrationNumber
            registrationState = dto.registrationState
            yearsOfExperience = dto.yearsOfExperience
            telemedicineReady = dto.telemedicineReady
            languagesSpoken = dto.languagesSpoken
            isActive = dto.isActive
            facilityId = dto.facilityId
            address = Address().apply {
                addressLine1 = dto.address.addressLine1
                addressLine2 = dto.address.addressLine2
                city = dto.address.city
                state = dto.address.state
                postalCode = dto.address.postalCode
                country = dto.address.country
            }
            createdAt = dto.createdAt ?: LocalDateTime.now()
            updatedAt = null
        }

        staffRepo.persist(staff)
        val uri = uriInfo.absolutePathBuilder.path(staff.staffId.toString()).build()
        return Response.created(uri).entity(StaffDto.fromEntity(staff)).build()
    }

    @PUT
    @Path("{id}")
    @Transactional
    fun update(@PathParam("id") id: UUID, dto: StaffDto): Response {
        val existing = staffRepo.findById(id)
            ?: return Response.status(Response.Status.NOT_FOUND).build()

        existing.apply {
            firstName = dto.firstName
            lastName = dto.lastName
            middleName = dto.middleName
            specializations = dto.specializations
            primarySpecialization = dto.primarySpecialization
            roleType = dto.roleType
            qualification = dto.qualification
            gender = dto.gender
            dateOfBirth = dto.dateOfBirth
            age = dto.age
            mobileNumber = dto.mobileNumber
            email = dto.email
            registrationNumber = dto.registrationNumber
            registrationState = dto.registrationState
            yearsOfExperience = dto.yearsOfExperience
            telemedicineReady = dto.telemedicineReady
            languagesSpoken = dto.languagesSpoken
            isActive = dto.isActive
            facilityId = dto.facilityId
            address = Address().apply {
                addressLine1 = dto.address.addressLine1
                addressLine2 = dto.address.addressLine2
                city = dto.address.city
                state = dto.address.state
                postalCode = dto.address.postalCode
                country = dto.address.country
            }
            updatedAt = LocalDateTime.now()
        }

        return Response.ok(StaffDto.fromEntity(existing)).build()
    }

    @DELETE
    @Path("{id}")
    @Transactional
    fun delete(@PathParam("id") id: UUID): Response {
        val deleted = staffRepo.deleteById(id)
        return if (deleted) Response.noContent().build()
        else Response.status(Response.Status.NOT_FOUND).build()
    }
}
