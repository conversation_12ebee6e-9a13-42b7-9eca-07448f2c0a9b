package sirobilt.meghasanjivini.common.model

import jakarta.persistence.Embeddable
import jakarta.persistence.Column

@Embeddable
class Address {

    @Column(name = "address_line_1", length = 255)
    var addressLine1: String? = null

    @Column(name = "address_line_2", length = 255)
    var addressLine2: String? = null

    @Column(name = "city", length = 100)
    var city: String? = null

    @Column(name = "state", length = 100)
    var state: String? = null

    @Column(name = "postal_code", length = 20)
    var postalCode: String? = null

    @Column(name = "country", length = 100)
    var country: String? = "India"
}
