package sirobilt.meghasanjivini.common.exception

import jakarta.ws.rs.core.Response
import jakarta.ws.rs.ext.ExceptionMapper
import jakarta.ws.rs.ext.Provider
import sirobilt.meghasanjivini.common.exception.dto.ErrorResponse

@Provider
class IllegalStateExceptionMapper : ExceptionMapper<IllegalStateException> {
    override fun toResponse(exception: IllegalStateException): Response {
        val errorResponse = ErrorResponse(
            status = Response.Status.BAD_REQUEST.statusCode,
            error = "Bad Request",
            message = exception.message ?: "Invalid state"
        )
        return Response.status(Response.Status.BAD_REQUEST)
            .entity(errorResponse)
            .build()
    }
}
