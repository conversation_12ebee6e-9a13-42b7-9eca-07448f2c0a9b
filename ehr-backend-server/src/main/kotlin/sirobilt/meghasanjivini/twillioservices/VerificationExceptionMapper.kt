package sirobilt.meghasanjivini.twillioservices

import jakarta.ws.rs.core.Response
import jakarta.ws.rs.ext.ExceptionMapper
import jakarta.ws.rs.ext.Provider

@Provider
class VerificationExceptionMapper : ExceptionMapper<VerificationFailedException> {
    override fun toResponse(exception: VerificationFailedException): Response {
        return Response.status(Response.Status.BAD_REQUEST)
            .entity(mapOf("error" to exception.message))
            .build()
    }
}
