package sirobilt.meghasanjivini.twillioservices

import com.twilio.exception.ApiException
import com.twilio.rest.verify.v2.service.Verification
import com.twilio.rest.verify.v2.service.VerificationCheck
import jakarta.enterprise.context.ApplicationScoped
import org.eclipse.microprofile.config.inject.ConfigProperty

@ApplicationScoped
class VerificationService {

    @ConfigProperty(name = "twilio.verify.service.sid")
    lateinit var verifyServiceSid: String

    fun sendVerificationCode(phoneNumber: String): String {
        val verification = Verification.creator(verifyServiceSid, phoneNumber, "sms").create()
        return verification.status  // should return "pending"
    }

    fun checkVerificationCode(phone: String, code: String): String {
        return try {
            val verificationCheck = VerificationCheck.creator(verifyServiceSid)
                .setTo(phone)
                .setCode(code)
                .create()

            if (verificationCheck.status != "approved") {
                throw VerificationFailedException("Incorrect OTP. Please re-enter the correct code.")
            }

            verificationCheck.status // e.g., "approved" or "pending"

        } catch (ex: ApiException) {
            println("❌ Twilio API Error: ${ex.message}")
            throw VerificationFailedException("Verification failed: ${ex.message}")
        }
    }
}
