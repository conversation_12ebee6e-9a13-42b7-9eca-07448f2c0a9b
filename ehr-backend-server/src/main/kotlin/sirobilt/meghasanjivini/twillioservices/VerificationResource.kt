package sirobilt.meghasanjivini.twillioservices

import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response


@Path("/mobile/verify")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
class VerificationResource {

    @Inject
    lateinit var verificationService: VerificationService

    @POST
    @Path("/send")
    fun sendCode(request: PhoneVerificationRequest): Response {
        val status = verificationService.sendVerificationCode(request.phoneNumber)
        return Response.ok(mapOf("status" to status)).build()
    }

    @POST
    @Path("/check")
    fun checkCode(request: VerifyCodeRequest): Response {
        val status = verificationService.checkVerificationCode(request.phoneNumber, request.code)
        return Response.ok(mapOf("status" to status)).build()
    }
}
